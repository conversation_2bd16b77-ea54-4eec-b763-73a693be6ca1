from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_
from typing import List, Optional
from datetime import datetime
import logging

from ..models.user import User, UserRole
from ..models.lab import Lab, LabAssignment, StudentGroup, StudentGroupMember
from ..models.virtual_machine import VirtualMachine
from ..database import get_db
from ..routers.auth import get_current_user
from ..schemas.lab import (
    LabCreate, LabUpdate, LabResponse, LabListResponse,
    LabAssignmentCreate, LabAssignmentUpdate, LabAssignmentResponse,
    StudentGroupCreate, StudentGroupUpdate, StudentGroupResponse, StudentGroupListResponse,
    StudentGroupMemberCreate, StudentGroupMemberResponse,
    LabStatsResponse, AssignStudentsToLabRequest, AssignVMsToLabRequest, BulkAssignmentResponse
)

router = APIRouter(prefix="/labs", tags=["labs"])
logger = logging.getLogger(__name__)

def require_instructor_or_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure only instructors or admins can access lab management endpoints."""
    if current_user.role not in [UserRole.TEACHER, UserRole.ADMIN]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Instructor or admin access required"
        )
    return current_user

def require_admin(current_user: User = Depends(get_current_user)) -> User:
    """Dependency to ensure only admins can access admin-only endpoints."""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

# Lab Management Endpoints

@router.get("/", response_model=LabListResponse)
async def list_labs(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None),
    instructor_id: Optional[int] = Query(None),
    is_active: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List labs with pagination and filtering."""
    logger.info(f"User {current_user.username} listing labs")
    
    # Build query
    query = db.query(Lab).options(
        joinedload(Lab.instructor),
        joinedload(Lab.students),
        joinedload(Lab.virtual_machines),
        joinedload(Lab.assignments)
    )
    
    # Filter by instructor for non-admin users
    if current_user.role == UserRole.TEACHER:
        query = query.filter(Lab.instructor_id == current_user.id)
    elif instructor_id and current_user.role == UserRole.ADMIN:
        query = query.filter(Lab.instructor_id == instructor_id)
    
    # Apply filters
    if search:
        search_filter = or_(
            Lab.name.ilike(f"%{search}%"),
            Lab.description.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    if is_active is not None:
        query = query.filter(Lab.is_active == is_active)
    
    # Apply sorting
    query = query.order_by(Lab.created_at.desc())
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * per_page
    labs = query.offset(offset).limit(per_page).all()
    
    # Build response
    lab_responses = []
    for lab in labs:
        lab_dict = {
            "id": lab.id,
            "name": lab.name,
            "description": lab.description,
            "instructor_id": lab.instructor_id,
            "instructor_name": lab.instructor.username if lab.instructor else None,
            "max_students": lab.max_students,
            "max_vms_per_student": lab.max_vms_per_student,
            "is_active": lab.is_active,
            "start_date": lab.start_date,
            "end_date": lab.end_date,
            "student_count": len(lab.students),
            "vm_count": len(lab.virtual_machines),
            "assignment_count": len(lab.assignments),
            "created_at": lab.created_at,
            "updated_at": lab.updated_at
        }
        lab_responses.append(LabResponse(**lab_dict))
    
    total_pages = (total + per_page - 1) // per_page
    
    return LabListResponse(
        labs=lab_responses,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages
    )

@router.get("/stats", response_model=LabStatsResponse)
async def get_lab_stats(
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Get lab statistics."""
    logger.info(f"User {current_user.username} requesting lab stats")
    
    # Filter by instructor for non-admin users
    lab_filter = Lab.instructor_id == current_user.id if current_user.role == UserRole.TEACHER else True
    
    # Get lab counts
    total_labs = db.query(Lab).filter(lab_filter).count()
    active_labs = db.query(Lab).filter(and_(lab_filter, Lab.is_active == True)).count()
    inactive_labs = total_labs - active_labs
    
    # Get assignment counts
    assignment_query = db.query(LabAssignment).join(Lab).filter(lab_filter)
    total_assignments = assignment_query.count()
    completed_assignments = assignment_query.filter(LabAssignment.is_completed == True).count()
    pending_assignments = total_assignments - completed_assignments
    
    # Get student enrollment count
    total_students_enrolled = db.query(func.count(func.distinct(Lab.students))).filter(lab_filter).scalar() or 0
    
    # Get group count
    group_filter = StudentGroup.instructor_id == current_user.id if current_user.role == UserRole.TEACHER else True
    total_groups = db.query(StudentGroup).filter(group_filter).count()
    
    return LabStatsResponse(
        total_labs=total_labs,
        active_labs=active_labs,
        inactive_labs=inactive_labs,
        total_assignments=total_assignments,
        completed_assignments=completed_assignments,
        pending_assignments=pending_assignments,
        total_students_enrolled=total_students_enrolled,
        total_groups=total_groups
    )

@router.post("/", response_model=LabResponse)
async def create_lab(
    lab_data: LabCreate,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Create a new lab."""
    logger.info(f"User {current_user.username} creating lab: {lab_data.name}")
    
    # Set instructor_id if not provided
    instructor_id = lab_data.instructor_id or current_user.id
    
    # Verify instructor exists and user has permission
    if current_user.role == UserRole.TEACHER and instructor_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Teachers can only create labs for themselves"
        )
    
    if current_user.role == UserRole.ADMIN and lab_data.instructor_id:
        instructor = db.query(User).filter(User.id == instructor_id).first()
        if not instructor or instructor.role not in [UserRole.TEACHER, UserRole.ADMIN]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid instructor ID"
            )
    
    try:
        # Create new lab
        db_lab = Lab(
            name=lab_data.name,
            description=lab_data.description,
            instructor_id=instructor_id,
            max_students=lab_data.max_students,
            max_vms_per_student=lab_data.max_vms_per_student,
            is_active=lab_data.is_active,
            start_date=lab_data.start_date,
            end_date=lab_data.end_date
        )
        
        db.add(db_lab)
        db.commit()
        db.refresh(db_lab)
        
        # Load instructor relationship
        db_lab = db.query(Lab).options(joinedload(Lab.instructor)).filter(Lab.id == db_lab.id).first()
        
        logger.info(f"Lab created successfully: {lab_data.name}")
        
        lab_dict = {
            "id": db_lab.id,
            "name": db_lab.name,
            "description": db_lab.description,
            "instructor_id": db_lab.instructor_id,
            "instructor_name": db_lab.instructor.username if db_lab.instructor else None,
            "max_students": db_lab.max_students,
            "max_vms_per_student": db_lab.max_vms_per_student,
            "is_active": db_lab.is_active,
            "start_date": db_lab.start_date,
            "end_date": db_lab.end_date,
            "student_count": 0,
            "vm_count": 0,
            "assignment_count": 0,
            "created_at": db_lab.created_at,
            "updated_at": db_lab.updated_at
        }
        return LabResponse(**lab_dict)
        
    except Exception as e:
        logger.error(f"Failed to create lab {lab_data.name}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create lab"
        )

@router.get("/{lab_id}", response_model=LabResponse)
async def get_lab(
    lab_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get lab details by ID."""
    lab = db.query(Lab).options(
        joinedload(Lab.instructor),
        joinedload(Lab.students),
        joinedload(Lab.virtual_machines),
        joinedload(Lab.assignments)
    ).filter(Lab.id == lab_id).first()
    
    if not lab:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lab not found"
        )
    
    # Check permissions
    if (current_user.role == UserRole.TEACHER and lab.instructor_id != current_user.id and 
        current_user.id not in [student.id for student in lab.students]):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    lab_dict = {
        "id": lab.id,
        "name": lab.name,
        "description": lab.description,
        "instructor_id": lab.instructor_id,
        "instructor_name": lab.instructor.username if lab.instructor else None,
        "max_students": lab.max_students,
        "max_vms_per_student": lab.max_vms_per_student,
        "is_active": lab.is_active,
        "start_date": lab.start_date,
        "end_date": lab.end_date,
        "student_count": len(lab.students),
        "vm_count": len(lab.virtual_machines),
        "assignment_count": len(lab.assignments),
        "created_at": lab.created_at,
        "updated_at": lab.updated_at
    }
    return LabResponse(**lab_dict)

@router.put("/{lab_id}", response_model=LabResponse)
async def update_lab(
    lab_id: int,
    lab_data: LabUpdate,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Update lab details."""
    logger.info(f"User {current_user.username} updating lab ID: {lab_id}")

    lab = db.query(Lab).filter(Lab.id == lab_id).first()
    if not lab:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lab not found"
        )

    # Check permissions
    if current_user.role == UserRole.TEACHER and lab.instructor_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own labs"
        )

    try:
        # Update fields if provided
        if lab_data.name is not None:
            lab.name = lab_data.name
        if lab_data.description is not None:
            lab.description = lab_data.description
        if lab_data.max_students is not None:
            lab.max_students = lab_data.max_students
        if lab_data.max_vms_per_student is not None:
            lab.max_vms_per_student = lab_data.max_vms_per_student
        if lab_data.is_active is not None:
            lab.is_active = lab_data.is_active
        if lab_data.start_date is not None:
            lab.start_date = lab_data.start_date
        if lab_data.end_date is not None:
            lab.end_date = lab_data.end_date

        db.commit()
        db.refresh(lab)

        # Load relationships
        lab = db.query(Lab).options(
            joinedload(Lab.instructor),
            joinedload(Lab.students),
            joinedload(Lab.virtual_machines),
            joinedload(Lab.assignments)
        ).filter(Lab.id == lab_id).first()

        logger.info(f"Lab updated successfully: {lab.name}")

        lab_dict = {
            "id": lab.id,
            "name": lab.name,
            "description": lab.description,
            "instructor_id": lab.instructor_id,
            "instructor_name": lab.instructor.username if lab.instructor else None,
            "max_students": lab.max_students,
            "max_vms_per_student": lab.max_vms_per_student,
            "is_active": lab.is_active,
            "start_date": lab.start_date,
            "end_date": lab.end_date,
            "student_count": len(lab.students),
            "vm_count": len(lab.virtual_machines),
            "assignment_count": len(lab.assignments),
            "created_at": lab.created_at,
            "updated_at": lab.updated_at
        }
        return LabResponse(**lab_dict)

    except Exception as e:
        logger.error(f"Failed to update lab {lab_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update lab"
        )

@router.delete("/{lab_id}")
async def delete_lab(
    lab_id: int,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Delete a lab."""
    logger.info(f"User {current_user.username} deleting lab ID: {lab_id}")

    lab = db.query(Lab).filter(Lab.id == lab_id).first()
    if not lab:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lab not found"
        )

    # Check permissions
    if current_user.role == UserRole.TEACHER and lab.instructor_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only delete your own labs"
        )

    try:
        db.delete(lab)
        db.commit()

        logger.info(f"Lab deleted successfully: {lab.name}")
        return {"message": "Lab deleted successfully"}

    except Exception as e:
        logger.error(f"Failed to delete lab {lab_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete lab"
        )

# Student Assignment Endpoints

@router.post("/{lab_id}/assign-students", response_model=BulkAssignmentResponse)
async def assign_students_to_lab(
    lab_id: int,
    request: AssignStudentsToLabRequest,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Assign students to a lab."""
    logger.info(f"User {current_user.username} assigning students to lab {lab_id}")

    lab = db.query(Lab).filter(Lab.id == lab_id).first()
    if not lab:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lab not found"
        )

    # Check permissions
    if current_user.role == UserRole.TEACHER and lab.instructor_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only assign students to your own labs"
        )

    success_count = 0
    failed_count = 0
    errors = []

    try:
        for student_id in request.student_ids:
            try:
                # Check if student exists and is a student
                student = db.query(User).filter(
                    User.id == student_id,
                    User.role == UserRole.STUDENT
                ).first()

                if not student:
                    errors.append(f"Student with ID {student_id} not found or not a student")
                    failed_count += 1
                    continue

                # Check if already assigned
                if student in lab.students:
                    errors.append(f"Student {student.username} is already assigned to this lab")
                    failed_count += 1
                    continue

                # Check lab capacity
                if len(lab.students) >= lab.max_students:
                    errors.append(f"Lab has reached maximum capacity ({lab.max_students} students)")
                    failed_count += 1
                    continue

                # Assign student to lab
                lab.students.append(student)
                success_count += 1

            except Exception as e:
                errors.append(f"Failed to assign student {student_id}: {str(e)}")
                failed_count += 1

        db.commit()
        logger.info(f"Assigned {success_count} students to lab {lab_id}")

        return BulkAssignmentResponse(
            success_count=success_count,
            failed_count=failed_count,
            errors=errors
        )

    except Exception as e:
        logger.error(f"Failed to assign students to lab {lab_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign students to lab"
        )

@router.post("/{lab_id}/assign-vms", response_model=BulkAssignmentResponse)
async def assign_vms_to_lab(
    lab_id: int,
    request: AssignVMsToLabRequest,
    current_user: User = Depends(require_instructor_or_admin),
    db: Session = Depends(get_db)
):
    """Assign VMs to a lab."""
    logger.info(f"User {current_user.username} assigning VMs to lab {lab_id}")

    lab = db.query(Lab).filter(Lab.id == lab_id).first()
    if not lab:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Lab not found"
        )

    # Check permissions
    if current_user.role == UserRole.TEACHER and lab.instructor_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only assign VMs to your own labs"
        )

    success_count = 0
    failed_count = 0
    errors = []

    try:
        for vm_id in request.vm_ids:
            try:
                # Check if VM exists
                vm = db.query(VirtualMachine).filter(VirtualMachine.id == vm_id).first()

                if not vm:
                    errors.append(f"VM with ID {vm_id} not found")
                    failed_count += 1
                    continue

                # Check if already assigned
                if vm in lab.virtual_machines:
                    errors.append(f"VM {vm.name} is already assigned to this lab")
                    failed_count += 1
                    continue

                # Assign VM to lab
                lab.virtual_machines.append(vm)
                success_count += 1

            except Exception as e:
                errors.append(f"Failed to assign VM {vm_id}: {str(e)}")
                failed_count += 1

        db.commit()
        logger.info(f"Assigned {success_count} VMs to lab {lab_id}")

        return BulkAssignmentResponse(
            success_count=success_count,
            failed_count=failed_count,
            errors=errors
        )

    except Exception as e:
        logger.error(f"Failed to assign VMs to lab {lab_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign VMs to lab"
        )
