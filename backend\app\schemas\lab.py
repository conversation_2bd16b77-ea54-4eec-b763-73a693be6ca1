from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime

# Lab Schemas
class LabBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    max_students: int = Field(default=30, ge=1, le=100)
    max_vms_per_student: int = Field(default=3, ge=1, le=10)
    is_active: bool = True
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class LabCreate(LabBase):
    instructor_id: Optional[int] = None  # Will be set from current user if not provided

class LabUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    max_students: Optional[int] = Field(None, ge=1, le=100)
    max_vms_per_student: Optional[int] = Field(None, ge=1, le=10)
    is_active: Optional[bool] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

class LabResponse(LabBase):
    id: int
    instructor_id: int
    instructor_name: Optional[str] = None
    student_count: int = 0
    vm_count: int = 0
    assignment_count: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class LabListResponse(BaseModel):
    labs: List[LabResponse]
    total: int
    page: int
    per_page: int
    total_pages: int

# Lab Assignment Schemas
class LabAssignmentBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    instructions: Optional[str] = None
    due_date: Optional[datetime] = None
    is_active: bool = True

class LabAssignmentCreate(LabAssignmentBase):
    lab_id: int
    student_id: int
    vm_id: Optional[int] = None

class LabAssignmentUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    instructions: Optional[str] = None
    due_date: Optional[datetime] = None
    is_active: Optional[bool] = None
    is_completed: Optional[bool] = None
    progress_percentage: Optional[int] = Field(None, ge=0, le=100)
    notes: Optional[str] = None

class LabAssignmentResponse(LabAssignmentBase):
    id: int
    lab_id: int
    student_id: int
    vm_id: Optional[int] = None
    lab_name: Optional[str] = None
    student_name: Optional[str] = None
    vm_name: Optional[str] = None
    is_completed: bool
    completion_date: Optional[datetime] = None
    progress_percentage: int
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# Student Group Schemas
class StudentGroupBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    max_members: int = Field(default=25, ge=1, le=100)
    is_active: bool = True

class StudentGroupCreate(StudentGroupBase):
    instructor_id: Optional[int] = None  # Will be set from current user if not provided

class StudentGroupUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    max_members: Optional[int] = Field(None, ge=1, le=100)
    is_active: Optional[bool] = None

class StudentGroupResponse(StudentGroupBase):
    id: int
    instructor_id: int
    instructor_name: Optional[str] = None
    member_count: int = 0
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class StudentGroupListResponse(BaseModel):
    groups: List[StudentGroupResponse]
    total: int
    page: int
    per_page: int
    total_pages: int

# Student Group Member Schemas
class StudentGroupMemberCreate(BaseModel):
    group_id: int
    student_id: int

class StudentGroupMemberResponse(BaseModel):
    id: int
    group_id: int
    student_id: int
    student_name: Optional[str] = None
    student_email: Optional[str] = None
    is_active: bool
    joined_date: datetime
    created_at: datetime

    class Config:
        from_attributes = True

# Lab Statistics
class LabStatsResponse(BaseModel):
    total_labs: int
    active_labs: int
    inactive_labs: int
    total_assignments: int
    completed_assignments: int
    pending_assignments: int
    total_students_enrolled: int
    total_groups: int

# Assignment to Lab/Group
class AssignStudentsToLabRequest(BaseModel):
    lab_id: int
    student_ids: List[int]

class AssignVMsToLabRequest(BaseModel):
    lab_id: int
    vm_ids: List[int]

class BulkAssignmentResponse(BaseModel):
    success_count: int
    failed_count: int
    errors: List[str] = []
