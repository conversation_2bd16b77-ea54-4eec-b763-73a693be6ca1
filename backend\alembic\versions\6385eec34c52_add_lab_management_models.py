"""Add lab management models

Revision ID: 6385eec34c52
Revises: 001
Create Date: 2025-07-08 15:26:10.911833

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '6385eec34c52'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('labs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('instructor_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('max_students', sa.Integer(), nullable=True),
    sa.Column('max_vms_per_student', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=True),
    sa.Column('end_date', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['instructor_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_labs_id'), 'labs', ['id'], unique=False)
    op.create_index(op.f('ix_labs_name'), 'labs', ['name'], unique=False)
    op.create_table('student_groups',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('instructor_id', sa.Integer(), nullable=False),
    sa.Column('max_members', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['instructor_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_student_groups_id'), 'student_groups', ['id'], unique=False)
    op.create_index(op.f('ix_student_groups_name'), 'student_groups', ['name'], unique=False)
    op.create_table('lab_assignments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('lab_id', sa.Integer(), nullable=False),
    sa.Column('student_id', sa.Integer(), nullable=False),
    sa.Column('vm_id', sa.Integer(), nullable=True),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('instructions', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_completed', sa.Boolean(), nullable=True),
    sa.Column('completion_date', sa.DateTime(), nullable=True),
    sa.Column('progress_percentage', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('due_date', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['lab_id'], ['labs.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['vm_id'], ['virtual_machines.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_lab_assignments_id'), 'lab_assignments', ['id'], unique=False)
    op.create_table('lab_students',
    sa.Column('lab_id', sa.Integer(), nullable=False),
    sa.Column('student_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['lab_id'], ['labs.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('lab_id', 'student_id')
    )
    op.create_table('lab_vms',
    sa.Column('lab_id', sa.Integer(), nullable=False),
    sa.Column('vm_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['lab_id'], ['labs.id'], ),
    sa.ForeignKeyConstraint(['vm_id'], ['virtual_machines.id'], ),
    sa.PrimaryKeyConstraint('lab_id', 'vm_id')
    )
    op.create_table('student_group_members',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.Column('student_id', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('joined_date', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['group_id'], ['student_groups.id'], ),
    sa.ForeignKeyConstraint(['student_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_student_group_members_id'), 'student_group_members', ['id'], unique=False)
    op.alter_column('users', 'is_active',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True)
    op.alter_column('users', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('users', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.drop_index(op.f('email'), table_name='users')
    op.drop_index(op.f('username'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.alter_column('virtual_machines', 'cpu_cores',
               existing_type=mysql.INTEGER(),
               nullable=True)
    op.alter_column('virtual_machines', 'memory_mb',
               existing_type=mysql.INTEGER(),
               nullable=True)
    op.alter_column('virtual_machines', 'disk_size',
               existing_type=mysql.INTEGER(),
               nullable=True)
    op.alter_column('virtual_machines', 'rdp_enabled',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True)
    op.alter_column('virtual_machines', 'ssh_enabled',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=True)
    op.alter_column('virtual_machines', 'cpu_usage',
               existing_type=mysql.FLOAT(),
               nullable=True)
    op.alter_column('virtual_machines', 'memory_usage',
               existing_type=mysql.FLOAT(),
               nullable=True)
    op.alter_column('virtual_machines', 'disk_usage',
               existing_type=mysql.FLOAT(),
               nullable=True)
    op.alter_column('virtual_machines', 'owner_id',
               existing_type=mysql.INTEGER(),
               nullable=True)
    op.alter_column('virtual_machines', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('virtual_machines', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.drop_constraint(op.f('virtual_machines_ibfk_1'), 'virtual_machines', type_='foreignkey')
    op.drop_index(op.f('ix_virtual_machines_owner_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_id'), 'virtual_machines', ['id'], unique=False)
    op.create_foreign_key(None, 'virtual_machines', 'users', ['owner_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'virtual_machines', type_='foreignkey')
    op.create_foreign_key(op.f('virtual_machines_ibfk_1'), 'virtual_machines', 'users', ['owner_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_virtual_machines_id'), table_name='virtual_machines')
    op.create_index(op.f('ix_virtual_machines_owner_id'), 'virtual_machines', ['owner_id'], unique=False)
    op.alter_column('virtual_machines', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.alter_column('virtual_machines', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('virtual_machines', 'owner_id',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.alter_column('virtual_machines', 'disk_usage',
               existing_type=mysql.FLOAT(),
               nullable=False)
    op.alter_column('virtual_machines', 'memory_usage',
               existing_type=mysql.FLOAT(),
               nullable=False)
    op.alter_column('virtual_machines', 'cpu_usage',
               existing_type=mysql.FLOAT(),
               nullable=False)
    op.alter_column('virtual_machines', 'ssh_enabled',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False)
    op.alter_column('virtual_machines', 'rdp_enabled',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False)
    op.alter_column('virtual_machines', 'disk_size',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.alter_column('virtual_machines', 'memory_mb',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.alter_column('virtual_machines', 'cpu_cores',
               existing_type=mysql.INTEGER(),
               nullable=False)
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=False)
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_index(op.f('username'), 'users', ['username'], unique=True)
    op.create_index(op.f('email'), 'users', ['email'], unique=True)
    op.alter_column('users', 'updated_at',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'))
    op.alter_column('users', 'created_at',
               existing_type=mysql.DATETIME(),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('users', 'is_active',
               existing_type=mysql.TINYINT(display_width=1),
               nullable=False)
    op.drop_index(op.f('ix_student_group_members_id'), table_name='student_group_members')
    op.drop_table('student_group_members')
    op.drop_table('lab_vms')
    op.drop_table('lab_students')
    op.drop_index(op.f('ix_lab_assignments_id'), table_name='lab_assignments')
    op.drop_table('lab_assignments')
    op.drop_index(op.f('ix_student_groups_name'), table_name='student_groups')
    op.drop_index(op.f('ix_student_groups_id'), table_name='student_groups')
    op.drop_table('student_groups')
    op.drop_index(op.f('ix_labs_name'), table_name='labs')
    op.drop_index(op.f('ix_labs_id'), table_name='labs')
    op.drop_table('labs')
    # ### end Alembic commands ###
